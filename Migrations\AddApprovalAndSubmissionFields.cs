using Microsoft.EntityFrameworkCore;
using DriverManagementSystem.Data;
using System;
using System.Threading.Tasks;

namespace DriverManagementSystem.Migrations
{
    /// <summary>
    /// إضافة حقول الموافقة على السفر ووقت الإرسال
    /// </summary>
    public class AddApprovalAndSubmissionFields
    {
        public static async Task ApplyAsync(ApplicationDbContext context)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("🔄 بدء إضافة حقول الموافقة ووقت الإرسال...");

                // التحقق من وجود الحقول أولاً
                var checkApprovalSql = @"
                    SELECT COUNT(*) 
                    FROM pragma_table_info('FieldVisits') 
                    WHERE name = 'ApprovalBy'";

                var checkSubmissionSql = @"
                    SELECT COUNT(*) 
                    FROM pragma_table_info('FieldVisits') 
                    WHERE name = 'SubmissionTime'";

                var approvalExists = await context.Database.ExecuteSqlRawAsync($"SELECT CASE WHEN EXISTS({checkApprovalSql}) THEN 1 ELSE 0 END");
                var submissionExists = await context.Database.ExecuteSqlRawAsync($"SELECT CASE WHEN EXISTS({checkSubmissionSql}) THEN 1 ELSE 0 END");

                // إضافة حقل الموافقة على السفر إذا لم يكن موجوداً
                if (approvalExists == 0)
                {
                    var addApprovalSql = @"
                        ALTER TABLE FieldVisits 
                        ADD COLUMN ApprovalBy TEXT DEFAULT ''";

                    await context.Database.ExecuteSqlRawAsync(addApprovalSql);
                    System.Diagnostics.Debug.WriteLine("✅ تم إضافة حقل ApprovalBy");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("ℹ️ حقل ApprovalBy موجود مسبقاً");
                }

                // إضافة حقل وقت الإرسال إذا لم يكن موجوداً
                if (submissionExists == 0)
                {
                    var addSubmissionSql = @"
                        ALTER TABLE FieldVisits 
                        ADD COLUMN SubmissionTime TEXT NULL";

                    await context.Database.ExecuteSqlRawAsync(addSubmissionSql);
                    System.Diagnostics.Debug.WriteLine("✅ تم إضافة حقل SubmissionTime");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("ℹ️ حقل SubmissionTime موجود مسبقاً");
                }

                System.Diagnostics.Debug.WriteLine("✅ تم تطبيق migration إضافة حقول الموافقة ووقت الإرسال بنجاح");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في تطبيق migration إضافة حقول الموافقة ووقت الإرسال: {ex.Message}");
                throw;
            }
        }
    }
}
