using Microsoft.EntityFrameworkCore;
using DriverManagementSystem.Data;
using System;
using System.Threading.Tasks;

namespace DriverManagementSystem.Migrations
{
    /// <summary>
    /// إضافة حقول الموافقة على السفر ووقت الإرسال
    /// </summary>
    public class AddApprovalAndSubmissionFields
    {
        public static async Task ApplyAsync(ApplicationDbContext context)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("🔄 بدء إضافة حقول الموافقة ووقت الإرسال...");

                // إضافة حقل الموافقة على السفر
                var addApprovalSql = @"
                    ALTER TABLE FieldVisits
                    ADD COLUMN ApprovalBy TEXT DEFAULT ''";

                await context.Database.ExecuteSqlRawAsync(addApprovalSql);
                System.Diagnostics.Debug.WriteLine("✅ تم إضافة حقل ApprovalBy");

                // إضافة حقل وقت الإرسال
                var addSubmissionSql = @"
                    ALTER TABLE FieldVisits
                    ADD COLUMN SubmissionTime TEXT NULL";

                await context.Database.ExecuteSqlRawAsync(addSubmissionSql);
                System.Diagnostics.Debug.WriteLine("✅ تم إضافة حقل SubmissionTime");

                System.Diagnostics.Debug.WriteLine("✅ تم تطبيق migration إضافة حقول الموافقة ووقت الإرسال بنجاح");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في تطبيق migration إضافة حقول الموافقة ووقت الإرسال: {ex.Message}");
                // لا نرمي الخطأ إذا كانت الحقول موجودة مسبقاً
                if (!ex.Message.Contains("duplicate column name"))
                {
                    throw;
                }
            }
        }
    }
}
