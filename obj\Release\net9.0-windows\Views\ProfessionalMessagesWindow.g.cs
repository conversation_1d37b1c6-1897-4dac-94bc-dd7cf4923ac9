﻿#pragma checksum "..\..\..\..\Views\ProfessionalMessagesWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "F8D4C9DFA076EA9B3997418DF0D0DA120B76C669"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using SFDSystem.Views;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace SFDSystem.Views {
    
    
    /// <summary>
    /// ProfessionalMessagesWindow
    /// </summary>
    public partial class ProfessionalMessagesWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector, System.Windows.Markup.IStyleConnector {
        
        
        #line 238 "..\..\..\..\Views\ProfessionalMessagesWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox ForshanalFilter;
        
        #line default
        #line hidden
        
        
        #line 252 "..\..\..\..\Views\ProfessionalMessagesWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox KanterFilter;
        
        #line default
        #line hidden
        
        
        #line 266 "..\..\..\..\Views\ProfessionalMessagesWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox HiluxFilter;
        
        #line default
        #line hidden
        
        
        #line 280 "..\..\..\..\Views\ProfessionalMessagesWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox BusFilter;
        
        #line default
        #line hidden
        
        
        #line 294 "..\..\..\..\Views\ProfessionalMessagesWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox PradoFilter;
        
        #line default
        #line hidden
        
        
        #line 416 "..\..\..\..\Views\ProfessionalMessagesWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid OffersResultsGrid;
        
        #line default
        #line hidden
        
        
        #line 524 "..\..\..\..\Views\ProfessionalMessagesWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button OffersButton;
        
        #line default
        #line hidden
        
        
        #line 563 "..\..\..\..\Views\ProfessionalMessagesWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button EditOffersButton;
        
        #line default
        #line hidden
        
        
        #line 625 "..\..\..\..\Views\ProfessionalMessagesWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SaveOffersButton;
        
        #line default
        #line hidden
        
        
        #line 663 "..\..\..\..\Views\ProfessionalMessagesWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ClearOffersButton;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/SFDSystem;component/views/professionalmessageswindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Views\ProfessionalMessagesWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.ForshanalFilter = ((System.Windows.Controls.CheckBox)(target));
            
            #line 239 "..\..\..\..\Views\ProfessionalMessagesWindow.xaml"
            this.ForshanalFilter.Checked += new System.Windows.RoutedEventHandler(this.VehicleFilter_Changed);
            
            #line default
            #line hidden
            
            #line 239 "..\..\..\..\Views\ProfessionalMessagesWindow.xaml"
            this.ForshanalFilter.Unchecked += new System.Windows.RoutedEventHandler(this.VehicleFilter_Changed);
            
            #line default
            #line hidden
            return;
            case 2:
            this.KanterFilter = ((System.Windows.Controls.CheckBox)(target));
            
            #line 253 "..\..\..\..\Views\ProfessionalMessagesWindow.xaml"
            this.KanterFilter.Checked += new System.Windows.RoutedEventHandler(this.VehicleFilter_Changed);
            
            #line default
            #line hidden
            
            #line 253 "..\..\..\..\Views\ProfessionalMessagesWindow.xaml"
            this.KanterFilter.Unchecked += new System.Windows.RoutedEventHandler(this.VehicleFilter_Changed);
            
            #line default
            #line hidden
            return;
            case 3:
            this.HiluxFilter = ((System.Windows.Controls.CheckBox)(target));
            
            #line 267 "..\..\..\..\Views\ProfessionalMessagesWindow.xaml"
            this.HiluxFilter.Checked += new System.Windows.RoutedEventHandler(this.VehicleFilter_Changed);
            
            #line default
            #line hidden
            
            #line 267 "..\..\..\..\Views\ProfessionalMessagesWindow.xaml"
            this.HiluxFilter.Unchecked += new System.Windows.RoutedEventHandler(this.VehicleFilter_Changed);
            
            #line default
            #line hidden
            return;
            case 4:
            this.BusFilter = ((System.Windows.Controls.CheckBox)(target));
            
            #line 281 "..\..\..\..\Views\ProfessionalMessagesWindow.xaml"
            this.BusFilter.Checked += new System.Windows.RoutedEventHandler(this.VehicleFilter_Changed);
            
            #line default
            #line hidden
            
            #line 281 "..\..\..\..\Views\ProfessionalMessagesWindow.xaml"
            this.BusFilter.Unchecked += new System.Windows.RoutedEventHandler(this.VehicleFilter_Changed);
            
            #line default
            #line hidden
            return;
            case 5:
            this.PradoFilter = ((System.Windows.Controls.CheckBox)(target));
            
            #line 295 "..\..\..\..\Views\ProfessionalMessagesWindow.xaml"
            this.PradoFilter.Checked += new System.Windows.RoutedEventHandler(this.VehicleFilter_Changed);
            
            #line default
            #line hidden
            
            #line 295 "..\..\..\..\Views\ProfessionalMessagesWindow.xaml"
            this.PradoFilter.Unchecked += new System.Windows.RoutedEventHandler(this.VehicleFilter_Changed);
            
            #line default
            #line hidden
            return;
            case 6:
            this.OffersResultsGrid = ((System.Windows.Controls.DataGrid)(target));
            return;
            case 8:
            this.OffersButton = ((System.Windows.Controls.Button)(target));
            
            #line 524 "..\..\..\..\Views\ProfessionalMessagesWindow.xaml"
            this.OffersButton.Click += new System.Windows.RoutedEventHandler(this.OffersButton_Click);
            
            #line default
            #line hidden
            return;
            case 9:
            this.EditOffersButton = ((System.Windows.Controls.Button)(target));
            
            #line 563 "..\..\..\..\Views\ProfessionalMessagesWindow.xaml"
            this.EditOffersButton.Click += new System.Windows.RoutedEventHandler(this.EditOffersButton_Click);
            
            #line default
            #line hidden
            return;
            case 10:
            this.SaveOffersButton = ((System.Windows.Controls.Button)(target));
            
            #line 625 "..\..\..\..\Views\ProfessionalMessagesWindow.xaml"
            this.SaveOffersButton.Click += new System.Windows.RoutedEventHandler(this.SaveOffersButton_Click);
            
            #line default
            #line hidden
            return;
            case 11:
            this.ClearOffersButton = ((System.Windows.Controls.Button)(target));
            
            #line 663 "..\..\..\..\Views\ProfessionalMessagesWindow.xaml"
            this.ClearOffersButton.Click += new System.Windows.RoutedEventHandler(this.ClearOffersButton_Click);
            
            #line default
            #line hidden
            return;
            case 12:
            
            #line 741 "..\..\..\..\Views\ProfessionalMessagesWindow.xaml"
            ((System.Windows.Controls.ComboBox)(target)).SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.MessageTemplate_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 13:
            
            #line 840 "..\..\..\..\Views\ProfessionalMessagesWindow.xaml"
            ((System.Windows.Controls.RadioButton)(target)).MouseLeftButtonDown += new System.Windows.Input.MouseButtonEventHandler(this.SendOption_Click);
            
            #line default
            #line hidden
            return;
            case 14:
            
            #line 850 "..\..\..\..\Views\ProfessionalMessagesWindow.xaml"
            ((System.Windows.Controls.RadioButton)(target)).MouseLeftButtonDown += new System.Windows.Input.MouseButtonEventHandler(this.SendOption_Click);
            
            #line default
            #line hidden
            return;
            case 15:
            
            #line 860 "..\..\..\..\Views\ProfessionalMessagesWindow.xaml"
            ((System.Windows.Controls.RadioButton)(target)).MouseLeftButtonDown += new System.Windows.Input.MouseButtonEventHandler(this.SendOption_Click);
            
            #line default
            #line hidden
            return;
            case 16:
            
            #line 931 "..\..\..\..\Views\ProfessionalMessagesWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.CopyMessage_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        void System.Windows.Markup.IStyleConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 7:
            
            #line 488 "..\..\..\..\Views\ProfessionalMessagesWindow.xaml"
            ((System.Windows.Controls.TextBlock)(target)).MouseLeftButtonDown += new System.Windows.Input.MouseButtonEventHandler(this.ToggleWinnerStatus_Click);
            
            #line default
            #line hidden
            break;
            }
        }
    }
}

