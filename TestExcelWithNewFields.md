# 📊 اختبار الحقول الجديدة في Excel

## 🎯 الحقول المضافة

### 1. حقل الموافقة على السفر (`approval_by`)
```
القيم المتاحة:
- branch_manager = مدير الفرع
- branch_deputy = نائب مدير الفرع  
- program_officer = ضابط المشروع للبرنامج
```

### 2. حقل وقت الإرسال (`_submission_time`)
```
تنسيق التاريخ والوقت:
- 2025-01-15T14:30:25
- 2025/01/15 14:30:25
- أو أي تنسيق تاريخ ووقت صالح
```

## 📋 مثال على ملف Excel للاختبار

### الشيت الرئيسي: "نموذج استمارة الزيارة الميدانية"
| _index | visit_form_number | approval_by | _submission_time | field_days_count | start_date | end_date | sector | visitors | trip_purpose |
|--------|------------------|-------------|------------------|------------------|------------|----------|---------|----------|--------------|
| 1 | V2025001 | branch_manager | 2025-01-15T14:30:25 | 3 | 2025-01-20 | 2025-01-22 | QTAA | 20,21 | مراجعة المشاريع |
| 2 | V2025002 | branch_deputy | 2025-01-16T09:15:30 | 2 | 2025-01-25 | 2025-01-26 | training | 22,23 | تدريب الكوادر |

## 🔧 كيفية الاختبار

### 1. إنشاء ملف Excel:
- أضف الحقلين الجديدين للشيت الرئيسي
- تأكد من تنسيق التاريخ والوقت صحيح
- استخدم القيم المحددة لحقل الموافقة

### 2. استيراد البيانات:
- افتح النظام
- اذهب لصفحة إدخال البيانات
- أدخل الرقم المرجعي
- اختر ملف Excel
- تحقق من استيراد الحقول الجديدة

### 3. عرض التقرير:
- اختر الزيارة المستوردة
- اضغط على زر التقرير
- تحقق من صفحة الاستمارة:
  - وقت الإرسال يظهر بارزاً في الأعلى
  - سؤال الموافقة يظهر فقط إذا كان رقم الزيارة فارغ

## 📝 ملاحظات مهمة

### حقل الموافقة:
- يظهر فقط عندما يكون رقم الزيارة فارغ
- مخفي في واجهة إدخال البيانات
- يحفظ في قاعدة البيانات تلقائياً

### حقل وقت الإرسال:
- يظهر بارزاً في أعلى الاستمارة
- يتم تنسيقه تلقائياً (YYYY/MM/DD - HH:MM:SS)
- يعتبر أساس الاستمارة

## 🎨 التصميم في الاستمارة

### وقت الإرسال:
- إطار أزرق بارز في أعلى الصفحة
- خط كبير وواضح
- أيقونة تقويم

### سؤال الموافقة:
- إطار أحمر فاتح
- يظهر فقط عند الحاجة
- نص واضح ومفهوم

## ✅ التحقق من النجاح

1. **استيراد ناجح:** البيانات تظهر في النظام
2. **حفظ صحيح:** الحقول محفوظة في قاعدة البيانات
3. **عرض سليم:** الاستمارة تظهر البيانات بشكل صحيح
4. **شروط العرض:** سؤال الموافقة يظهر/يختفي حسب الشروط
